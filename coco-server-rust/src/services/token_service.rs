use crate::error::error::CocoError;
use crate::models::access_token::{generate_api_token_name, AccessToken};
use crate::repositories::token_repository::TokenRepository;
use std::sync::Arc;
use tracing::{debug, error, info};

/// API令牌管理服务
#[derive(Clone)]
pub struct TokenService {
    /// 令牌数据访问层
    repository: Arc<TokenRepository>,
}

impl TokenService {
    /// 创建新的令牌服务实例
    pub fn new(repository: Arc<TokenRepository>) -> Self {
        Self { repository }
    }

    /// 生成新的API令牌
    pub async fn generate_api_token(
        &self,
        user_id: &str,
        name: Option<String>,
    ) -> Result<AccessToken, CocoError> {
        let token_name = name.unwrap_or_else(|| generate_api_token_name(None));
        let token = AccessToken::new(user_id.to_string(), token_name);

        info!(
            "Generating API token for user: {}, token_id: {}, name: {}",
            user_id, token.id, token.name
        );

        // 存储令牌到数据库
        self.repository
            .create_token(&token)
            .await
            .map_err(|e| CocoError::server(&format!("Failed to create token: {}", e)))?;

        debug!("API token generated successfully: {}", token.id);
        Ok(token)
    }

    /// 验证API令牌
    pub async fn validate_api_token(&self, token: &str) -> Result<AccessToken, CocoError> {
        match self.repository.find_by_token(token).await {
            Ok(Some(access_token)) => {
                if !access_token.is_active {
                    error!("Token is inactive: {}", access_token.id);
                    return Err(CocoError::unauthorized("Token is inactive"));
                }

                if access_token.is_expired() {
                    error!("Token is expired: {}", access_token.id);
                    return Err(CocoError::unauthorized("Token is expired"));
                }

                debug!("Token validation successful: {}", access_token.id);
                Ok(access_token)
            }
            Ok(None) => {
                debug!("Token not found: {}", token);
                Err(CocoError::unauthorized("Invalid token"))
            }
            Err(e) => {
                error!("Database error during token validation: {}", e);
                Err(CocoError::server("Token validation failed"))
            }
        }
    }

    /// 撤销令牌
    pub async fn revoke_token(&self, token_id: &str, user_id: &str) -> Result<(), CocoError> {
        info!("Revoking token: {} for user: {}", token_id, user_id);

        // 验证令牌属于指定用户
        match self.repository.find_by_id(token_id).await {
            Ok(Some(token)) => {
                if token.user_id != user_id {
                    error!(
                        "Permission denied: token {} does not belong to user {}",
                        token_id, user_id
                    );
                    return Err(CocoError::not_found("Token not found or permission denied"));
                }

                // 撤销令牌
                self.repository
                    .revoke_token(token_id)
                    .await
                    .map_err(|e| CocoError::server(&format!("Failed to revoke token: {}", e)))?;

                info!("Token revoked successfully: {}", token_id);
                Ok(())
            }
            Ok(None) => {
                error!("Token not found: {}", token_id);
                Err(CocoError::not_found("Token not found"))
            }
            Err(e) => {
                error!("Database error during token revocation: {}", e);
                Err(CocoError::server("Token revocation failed"))
            }
        }
    }

    /// 获取用户的所有令牌
    pub async fn get_user_tokens(&self, user_id: &str) -> Result<Vec<AccessToken>, CocoError> {
        debug!("Getting tokens for user: {}", user_id);

        let user_tokens = self.user_tokens.read().await;
        let tokens = self.tokens.read().await;

        let token_ids = user_tokens.get(user_id).cloned().unwrap_or_default();
        let mut user_token_list = Vec::new();

        for (_, token) in tokens.iter() {
            if token.user_id == user_id && token_ids.contains(&token.id) {
                user_token_list.push(token.clone());
            }
        }

        debug!(
            "Found {} tokens for user: {}",
            user_token_list.len(),
            user_id
        );
        Ok(user_token_list)
    }

    /// 重命名令牌
    pub async fn rename_token(
        &self,
        token_id: &str,
        user_id: &str,
        new_name: String,
    ) -> Result<(), CocoError> {
        info!(
            "Renaming token: {} to: {} for user: {}",
            token_id, new_name, user_id
        );

        let mut tokens = self.tokens.write().await;
        let mut found = false;

        for (_, token) in tokens.iter_mut() {
            if token.id == token_id && token.user_id == user_id {
                token.name = new_name.clone();
                found = true;
                break;
            }
        }

        if found {
            info!("Token renamed successfully: {}", token_id);
            Ok(())
        } else {
            error!("Token not found or permission denied: {}", token_id);
            Err(CocoError::not_found("Token not found or permission denied"))
        }
    }

    /// 更新令牌最后使用时间
    pub async fn update_last_used(&self, token: &str) -> Result<(), CocoError> {
        let mut tokens = self.tokens.write().await;

        if let Some(access_token) = tokens.get_mut(token) {
            access_token.update_last_used();
            debug!("Updated last used time for token: {}", access_token.id);
            Ok(())
        } else {
            Err(CocoError::not_found("Token not found"))
        }
    }

    /// 获取令牌统计信息
    pub async fn get_token_stats(&self) -> HashMap<String, usize> {
        let tokens = self.tokens.read().await;
        let user_tokens = self.user_tokens.read().await;

        let mut stats = HashMap::new();
        stats.insert("total_tokens".to_string(), tokens.len());
        stats.insert("total_users".to_string(), user_tokens.len());

        let active_tokens = tokens.values().filter(|t| t.is_active).count();
        stats.insert("active_tokens".to_string(), active_tokens);

        let expired_tokens = tokens.values().filter(|t| t.is_expired()).count();
        stats.insert("expired_tokens".to_string(), expired_tokens);

        stats
    }

    /// 清理过期令牌
    pub async fn cleanup_expired_tokens(&self) -> usize {
        info!("Starting cleanup of expired tokens");

        let mut tokens = self.tokens.write().await;
        let mut user_tokens = self.user_tokens.write().await;

        let initial_count = tokens.len();
        let mut expired_token_ids = Vec::new();

        // 收集过期令牌
        for (token_str, token) in tokens.iter() {
            if token.is_expired() {
                expired_token_ids.push((
                    token_str.clone(),
                    token.id.clone(),
                    token.user_id.clone(),
                ));
            }
        }

        // 移除过期令牌
        for (token_str, token_id, user_id) in expired_token_ids.iter() {
            tokens.remove(token_str);

            // 从用户令牌映射中移除
            if let Some(user_token_list) = user_tokens.get_mut(user_id) {
                user_token_list.retain(|id| id != token_id);
            }
        }

        let cleaned_count = expired_token_ids.len();
        info!("Cleaned up {} expired tokens", cleaned_count);

        cleaned_count
    }
}

impl Default for TokenService {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{sleep, Duration};

    #[tokio::test]
    async fn test_generate_and_validate_token() {
        let service = TokenService::new();
        let user_id = "test-user";
        let token_name = Some("test-token".to_string());

        // 生成令牌
        let token = service
            .generate_api_token(user_id, token_name)
            .await
            .unwrap();
        assert_eq!(token.user_id, user_id);
        assert_eq!(token.name, "test-token");

        // 验证令牌
        let validated = service
            .validate_api_token(&token.access_token)
            .await
            .unwrap();
        assert_eq!(validated.id, token.id);
    }

    #[tokio::test]
    async fn test_revoke_token() {
        let service = TokenService::new();
        let user_id = "test-user";

        // 生成令牌
        let token = service.generate_api_token(user_id, None).await.unwrap();

        // 撤销令牌
        service.revoke_token(&token.id, user_id).await.unwrap();

        // 验证令牌应该失败
        let result = service.validate_api_token(&token.access_token).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_get_user_tokens() {
        let service = TokenService::new();
        let user_id = "test-user";

        // 生成多个令牌
        let _token1 = service
            .generate_api_token(user_id, Some("token1".to_string()))
            .await
            .unwrap();
        let _token2 = service
            .generate_api_token(user_id, Some("token2".to_string()))
            .await
            .unwrap();

        // 获取用户令牌
        let user_tokens = service.get_user_tokens(user_id).await.unwrap();
        assert_eq!(user_tokens.len(), 2);
    }

    #[tokio::test]
    async fn test_rename_token() {
        let service = TokenService::new();
        let user_id = "test-user";

        // 生成令牌
        let token = service
            .generate_api_token(user_id, Some("old-name".to_string()))
            .await
            .unwrap();

        // 重命名令牌
        service
            .rename_token(&token.id, user_id, "new-name".to_string())
            .await
            .unwrap();

        // 验证名称已更改
        let user_tokens = service.get_user_tokens(user_id).await.unwrap();
        assert_eq!(user_tokens[0].name, "new-name");
    }
}
