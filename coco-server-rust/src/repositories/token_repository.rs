use crate::database::SurrealDBClient;
use crate::error::Result;
use crate::models::access_token::AccessToken;
use std::sync::Arc;
use tracing::{debug, info};

/// 令牌数据访问层
#[derive(Clone)]
pub struct TokenRepository {
    /// SurrealDB客户端
    client: Arc<SurrealDBClient>,
}

impl TokenRepository {
    /// 创建新的令牌仓库实例
    pub fn new(client: Arc<SurrealDBClient>) -> Self {
        Self { client }
    }

    /// 创建新的访问令牌
    pub async fn create_token(&self, token: &AccessToken) -> Result<AccessToken> {
        debug!("创建访问令牌: {}", token.id);

        let result = self
            .client
            .db()
            .query(
                "
                CREATE access_tokens SET
                    id = $id,
                    access_token = $access_token,
                    user_id = $user_id,
                    name = $name,
                    provider = $provider,
                    token_type = $token_type,
                    roles = $roles,
                    permissions = $permissions,
                    expire_in = $expire_in,
                    created_at = $created_at,
                    last_used = $last_used,
                    is_active = $is_active
                ",
            )
            .bind(("id", &token.id))
            .bind(("access_token", &token.access_token))
            .bind(("user_id", &token.user_id))
            .bind(("name", &token.name))
            .bind(("provider", &token.provider))
            .bind(("token_type", &token.token_type))
            .bind(("roles", &token.roles))
            .bind(("permissions", &token.permissions))
            .bind(("expire_in", token.expire_in))
            .bind(("created_at", token.created_at))
            .bind(("last_used", token.last_used))
            .bind(("is_active", token.is_active))
            .await
            .map_err(|e| crate::error::Error::Database(format!("创建令牌失败: {}", e)))?;

        info!("访问令牌创建成功: {}", token.id);
        Ok(token.clone())
    }

    /// 根据令牌字符串查找访问令牌
    pub async fn find_by_token(&self, token: &str) -> Result<Option<AccessToken>> {
        debug!("查找访问令牌: {}", token);

        let mut result = self
            .client
            .db()
            .query("SELECT * FROM access_tokens WHERE access_token = $token AND is_active = true")
            .bind(("token", token))
            .await
            .map_err(|e| crate::error::Error::Database(format!("查找令牌失败: {}", e)))?;

        let tokens: Vec<AccessToken> = result
            .take(0)
            .map_err(|e| crate::error::Error::Database(format!("解析令牌数据失败: {}", e)))?;

        Ok(tokens.into_iter().next())
    }

    /// 根据令牌ID查找访问令牌
    pub async fn find_by_id(&self, token_id: &str) -> Result<Option<AccessToken>> {
        debug!("根据ID查找访问令牌: {}", token_id);

        let mut result = self
            .client
            .db()
            .query("SELECT * FROM access_tokens WHERE id = $id")
            .bind(("id", token_id))
            .await
            .map_err(|e| crate::error::Error::Database(format!("查找令牌失败: {}", e)))?;

        let tokens: Vec<AccessToken> = result
            .take(0)
            .map_err(|e| crate::error::Error::Database(format!("解析令牌数据失败: {}", e)))?;

        Ok(tokens.into_iter().next())
    }

    /// 获取用户的所有访问令牌
    pub async fn find_by_user_id(&self, user_id: &str) -> Result<Vec<AccessToken>> {
        debug!("获取用户的所有访问令牌: {}", user_id);

        let mut result = self
            .client
            .db()
            .query("SELECT * FROM access_tokens WHERE user_id = $user_id ORDER BY created_at DESC")
            .bind(("user_id", user_id))
            .await
            .map_err(|e| crate::error::Error::Database(format!("查找用户令牌失败: {}", e)))?;

        let tokens: Vec<AccessToken> = result
            .take(0)
            .map_err(|e| crate::error::Error::Database(format!("解析令牌数据失败: {}", e)))?;

        Ok(tokens)
    }

    /// 更新令牌的最后使用时间
    pub async fn update_last_used(&self, token: &str) -> Result<()> {
        debug!("更新令牌最后使用时间: {}", token);

        self.client
            .db()
            .query("UPDATE access_tokens SET last_used = time::now() WHERE access_token = $token")
            .bind(("token", token))
            .await
            .map_err(|e| crate::error::Error::Database(format!("更新令牌使用时间失败: {}", e)))?;

        Ok(())
    }

    /// 撤销令牌（设置为非活跃状态）
    pub async fn revoke_token(&self, token_id: &str) -> Result<()> {
        debug!("撤销访问令牌: {}", token_id);

        self.client
            .db()
            .query("UPDATE access_tokens SET is_active = false WHERE id = $id")
            .bind(("id", token_id))
            .await
            .map_err(|e| crate::error::Error::Database(format!("撤销令牌失败: {}", e)))?;

        info!("访问令牌已撤销: {}", token_id);
        Ok(())
    }

    /// 删除过期的令牌
    pub async fn delete_expired_tokens(&self) -> Result<usize> {
        debug!("删除过期的访问令牌");

        let current_timestamp = chrono::Utc::now().timestamp();

        let mut result = self
            .client
            .db()
            .query("DELETE access_tokens WHERE expire_in < $current_time RETURN BEFORE")
            .bind(("current_time", current_timestamp))
            .await
            .map_err(|e| crate::error::Error::Database(format!("删除过期令牌失败: {}", e)))?;

        let deleted_tokens: Vec<AccessToken> = result
            .take(0)
            .map_err(|e| crate::error::Error::Database(format!("解析删除结果失败: {}", e)))?;

        let count = deleted_tokens.len();
        info!("删除了 {} 个过期的访问令牌", count);
        Ok(count)
    }

    /// 重命名令牌
    pub async fn rename_token(&self, token_id: &str, new_name: &str) -> Result<()> {
        debug!("重命名访问令牌: {} -> {}", token_id, new_name);

        self.client
            .db()
            .query("UPDATE access_tokens SET name = $name WHERE id = $id")
            .bind(("name", new_name))
            .bind(("id", token_id))
            .await
            .map_err(|e| crate::error::Error::Database(format!("重命名令牌失败: {}", e)))?;

        info!("访问令牌重命名成功: {}", token_id);
        Ok(())
    }

    /// 获取活跃令牌数量
    pub async fn count_active_tokens(&self, user_id: &str) -> Result<usize> {
        debug!("统计用户活跃令牌数量: {}", user_id);

        let mut result = self
            .client
            .db()
            .query(
                "SELECT count() FROM access_tokens WHERE user_id = $user_id AND is_active = true",
            )
            .bind(("user_id", user_id))
            .await
            .map_err(|e| crate::error::Error::Database(format!("统计令牌数量失败: {}", e)))?;

        let count_result: Option<serde_json::Value> = result
            .take(0)
            .map_err(|e| crate::error::Error::Database(format!("解析统计结果失败: {}", e)))?;

        let count = count_result
            .and_then(|v| v.get("count").and_then(|c| c.as_u64()))
            .unwrap_or(0) as usize;

        Ok(count)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::database::DatabaseConfig;
    use crate::models::access_token::AccessToken;

    #[tokio::test]
    #[ignore] // 需要运行中的SurrealDB实例
    async fn test_token_repository() {
        let config = DatabaseConfig::default();
        let client = Arc::new(SurrealDBClient::new(config).await.unwrap());
        let repo = TokenRepository::new(client);

        // 创建测试令牌
        let token = AccessToken::new("test-user".to_string(), "test-token".to_string());
        let created_token = repo.create_token(&token).await.unwrap();
        assert_eq!(created_token.id, token.id);

        // 根据令牌字符串查找
        let found_token = repo.find_by_token(&token.access_token).await.unwrap();
        assert!(found_token.is_some());
        assert_eq!(found_token.unwrap().id, token.id);

        // 根据用户ID查找
        let user_tokens = repo.find_by_user_id("test-user").await.unwrap();
        assert!(!user_tokens.is_empty());

        // 撤销令牌
        repo.revoke_token(&token.id).await.unwrap();

        // 验证令牌已被撤销
        let revoked_token = repo.find_by_token(&token.access_token).await.unwrap();
        assert!(revoked_token.is_none()); // 因为查询条件包含 is_active = true
    }
}
