import { useMemo, memo } from "react";

import { Status } from "@/types/server";

type StatusIndicatorProps = {
  enabled: boolean;
  public: boolean;
  hasProfile: boolean;
  status?: Status;
  available?: boolean;
};

const StatusIndicator = memo(
  ({ enabled, public: isPublic, hasProfile, status, available = true }: StatusIndicatorProps) => {
    // If service is enabled AND (public OR (private AND logged in)) AND service is available
    // Otherwise show gray status
    const isActive =
      enabled && (isPublic || (!isPublic && hasProfile)) && status;

    const statusColorClass = useMemo(() => {
      // 如果服务器不可用，显示红色
      if (!available) return "bg-red-500";
      
      if (!isActive) return "bg-gray-400 dark:bg-gray-600";
      switch (status) {
        case "green":
          return "bg-green-500";
        case "yellow":
          return "bg-yellow-500";
        case "red":
          return "bg-red-500";
        default:
          return "bg-gray-400 dark:bg-gray-600";
      }
    }, [isActive, status, available]);

    // 添加工具提示以显示更详细的状态信息
    const statusTitle = useMemo(() => {
      if (!available) return "服务器不可用";
      if (!isActive) return "服务器未激活";
      
      switch (status) {
        case "green":
          return "服务器运行正常";
        case "yellow":
          return "服务器存在警告";
        case "red":
          return "服务器出现错误";
        default:
          return "服务器状态未知";
      }
    }, [isActive, status, available]);

    return (
      <div
        className={`w-3 h-3 rounded-full ${statusColorClass}`}
        role="status"
        aria-label={isActive ? "active" : "inactive"}
        title={statusTitle}
      />
    );
  }
);

export default StatusIndicator;